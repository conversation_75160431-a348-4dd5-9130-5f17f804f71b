/**
 * Utility functions for handling standardized API responses
 */

import { debug, warning } from "./logger.js";

/**
 * Extract data from a standardized API response
 *
 * @param {Object} response - The API response object
 * @param {boolean} fallbackToLegacy - Whether to fall back to legacy format if standardized format is not detected
 * @returns {Object|Array|null} The extracted data or null if no data is found
 */
export const extractResponseData = (response, fallbackToLegacy = true) => {
  // Debug the response structure
  debug("Extracting data from response", { response });

  // Check if response has the new standardized format
  if (
    response &&
    response.data &&
    response.data.success === true &&
    response.data.data !== undefined
  ) {
    debug("Found standardized response format with success=true");
    return response.data.data;
  }

  // Check for other common API response formats
  if (response && response.data) {
    // Check for data property in response.data
    if (response.data.data !== undefined) {
      debug("Found data property in response.data");
      return response.data.data;
    }

    // Check for results property in response.data
    if (response.data.results !== undefined) {
      debug("Found results property in response.data");
      return response.data.results;
    }

    // Check for items property in response.data
    if (response.data.items !== undefined) {
      debug("Found items property in response.data");
      return response.data.items;
    }

    // Check for skills property in response.data (specific to this API)
    if (response.data.skills !== undefined) {
      debug("Found skills property in response.data");
      return response.data.skills;
    }
  }

  // Fall back to legacy format if enabled
  if (fallbackToLegacy && response && response.data !== undefined) {
    debug("Using legacy format (direct response.data)");
    return response.data;
  }

  warning("No data found in response", { response });
  return null;
};

/**
 * Extract pagination metadata from a standardized API response
 *
 * @param {Object} response - The API response object
 * @returns {Object|null} The pagination metadata or null if not found
 */
export const extractPaginationMeta = (response) => {
  if (
    response &&
    response.data &&
    response.data.meta &&
    response.data.meta.pagination
  ) {
    return response.data.meta.pagination;
  }

  return null;
};

/**
 * Extract error information from a standardized API error response
 *
 * @param {Object} error - The error object from a catch block
 * @returns {Object} Standardized error information
 */
export const extractErrorInfo = (error) => {
  // Default error info
  const defaultErrorInfo = {
    message: "An unexpected error occurred",
    code: 500,
    type: "UnknownError",
    details: null,
  };

  // No error object
  if (!error) {
    return defaultErrorInfo;
  }

  // Check for standardized error format
  if (error.response && error.response.data && error.response.data.error) {
    const errorData = error.response.data.error;

    return {
      message: errorData.message || defaultErrorInfo.message,
      code: errorData.code || error.response.status || defaultErrorInfo.code,
      type: errorData.type || defaultErrorInfo.type,
      details: errorData.details || null,
    };
  }

  // Handle legacy error format
  if (error.response && error.response.data) {
    // Check if error.response.data is a string or has a detail property
    const errorMessage =
      typeof error.response.data === "string"
        ? error.response.data
        : error.response.data.detail || defaultErrorInfo.message;

    return {
      message: errorMessage,
      code: error.response.status || defaultErrorInfo.code,
      type: defaultErrorInfo.type,
      details: null,
    };
  }

  // Handle network errors
  if (error.message) {
    return {
      message: error.message,
      code: defaultErrorInfo.code,
      type: "NetworkError",
      details: null,
    };
  }

  return defaultErrorInfo;
};

/**
 * Check if a response contains an error
 *
 * @param {Object} response - The API response object
 * @returns {boolean} True if the response contains an error
 */
export const hasError = (response) => {
  if (!response || !response.data) {
    return true;
  }

  // Check standardized format
  if (response.data.success === false && response.data.error) {
    return true;
  }

  return false;
};
